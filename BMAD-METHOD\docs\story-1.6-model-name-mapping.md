# Story 1.6: Model Name Mapping

## Status: Draft

## Story

- As a user
- I want the bridge to use a configured map to translate model names
- so that when I request a generic model name, it is sent to the backend as the specific required model

## Acceptance Criteria (ACs)

1. **Model name mapping works in chat requests**
   - When a user sends a chat request with a mapped model name (e.g., "llama3"), the bridge translates it to the configured backend model (e.g., "gpt-4o-mini")
   - The mapping is applied before sending the request to the backend
   - The original model name is preserved in the response to the client

2. **Model name mapping works in model list responses**
   - When the bridge returns the model list via /api/tags, backend model names are reverse-mapped to user-friendly names
   - Models that have mappings show the mapped name instead of the backend model ID
   - Models without mappings show their original backend names

3. **Configuration supports flexible mapping rules**
   - Model mappings are configurable via environment variables (MODEL_MAPPINGS)
   - Supports JSON format for complex mapping configurations
   - Allows one-to-one model name translations
   - Configuration is validated at startup with clear error messages

4. **Fallback behavior for unmapped models**
   - If a requested model name has no mapping, use the original name as-is
   - Log a warning when unmapped models are requested
   - Continue processing without failing the request

5. **Clear error messages for invalid model names**
   - If the backend rejects a model name (mapped or unmapped), return a clear error to the client
   - Include the original requested model name in error messages
   - Provide helpful suggestions when possible

## Tasks / Subtasks

- [ ] Task 1: Extend configuration to support model mappings (AC: 3)
  - [ ] Add MODEL_MAPPINGS field to Settings class in app/core/config.py
  - [ ] Implement JSON parsing and validation for model mappings
  - [ ] Add default empty mapping configuration
  - [ ] Update .env.example with model mapping examples
  - [ ] Add configuration validation tests

- [ ] Task 2: Implement model name translation logic (AC: 1, 4, 5)
  - [ ] Enhance ChatTranslationStrategy._map_model_name() method
  - [ ] Add reverse mapping functionality for model list responses
  - [ ] Implement fallback behavior for unmapped models
  - [ ] Add logging for mapping operations and warnings
  - [ ] Create utility functions for bidirectional mapping

- [ ] Task 3: Integrate mapping into chat translation (AC: 1)
  - [ ] Verify existing chat translation uses model mapping correctly
  - [ ] Ensure original model name is preserved in responses
  - [ ] Test with various mapped and unmapped model names
  - [ ] Validate error handling for invalid mapped models

- [ ] Task 4: Integrate mapping into model list translation (AC: 2)
  - [ ] Implement reverse mapping in ModelListTranslationStrategy
  - [ ] Update translate_model_list() to apply reverse mappings
  - [ ] Ensure unmapped models show original names
  - [ ] Test model list with mixed mapped/unmapped models

- [ ] Task 5: Add validation for model mapping configuration (AC: 3, 5)
  - [ ] Validate JSON format of MODEL_MAPPINGS at startup
  - [ ] Check for circular mappings or invalid configurations
  - [ ] Provide clear error messages for configuration issues
  - [ ] Add configuration validation to health check endpoint

- [ ] Task 6: Write comprehensive tests for mapping logic (AC: 1-5)
  - [ ] Unit tests for bidirectional model name mapping
  - [ ] Integration tests for chat requests with mapped models
  - [ ] Integration tests for model list with reverse mapping
  - [ ] Error scenario tests for invalid configurations
  - [ ] Performance tests for mapping operations

## Dev Technical Guidance

### Model Mapping Configuration Format
```json
{
  "llama3": "gpt-4o-mini",
  "codellama": "gpt-4",
  "mistral": "claude-3-sonnet",
  "phi": "gpt-3.5-turbo"
}
```

### Implementation Approach
1. **Bidirectional Mapping**: Create both forward (user→backend) and reverse (backend→user) mapping dictionaries
2. **Lazy Loading**: Parse and validate mappings once at startup, cache for performance
3. **Case Sensitivity**: Implement case-insensitive matching for user convenience
4. **Logging Strategy**: Log all mapping operations at DEBUG level, warnings for unmapped models

### Key Classes to Modify
- `app/core/config.py`: Add MODEL_MAPPINGS configuration
- `app/services/translation_service.py`: Enhance mapping logic in both strategies
- `app/api/routers/chat.py`: Ensure error messages include original model names
- `app/api/routers/models.py`: Apply reverse mapping in model list responses

### Testing Strategy
- **Unit Tests**: Focus on mapping logic isolation
- **Integration Tests**: End-to-end scenarios with real model names
- **Configuration Tests**: Various mapping configurations and edge cases
- **Performance Tests**: Ensure mapping doesn't add significant latency

## Story Progress Notes

### Agent Model Used: `Claude Sonnet 4`

### Completion Notes List

{Implementation notes will be added during development}

### Change Log

- **2024-12-19**: Initial story creation based on PRD requirements and Epic 1 completion status
